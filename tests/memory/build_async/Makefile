# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Atom/tests/memory

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Atom/tests/memory/build_async

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Atom/tests/memory/build_async/CMakeFiles /home/<USER>/Atom/tests/memory/build_async//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Atom/tests/memory/build_async/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named async_memory_tests

# Build rule for target.
async_memory_tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 async_memory_tests
.PHONY : async_memory_tests

# fast build rule for target.
async_memory_tests/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/async_memory_tests.dir/build.make CMakeFiles/async_memory_tests.dir/build
.PHONY : async_memory_tests/fast

test_async_comprehensive.o: test_async_comprehensive.cpp.o
.PHONY : test_async_comprehensive.o

# target to build an object file
test_async_comprehensive.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/async_memory_tests.dir/build.make CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.o
.PHONY : test_async_comprehensive.cpp.o

test_async_comprehensive.i: test_async_comprehensive.cpp.i
.PHONY : test_async_comprehensive.i

# target to preprocess a source file
test_async_comprehensive.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/async_memory_tests.dir/build.make CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.i
.PHONY : test_async_comprehensive.cpp.i

test_async_comprehensive.s: test_async_comprehensive.cpp.s
.PHONY : test_async_comprehensive.s

# target to generate assembly for a file
test_async_comprehensive.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/async_memory_tests.dir/build.make CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.s
.PHONY : test_async_comprehensive.cpp.s

test_async_integration.o: test_async_integration.cpp.o
.PHONY : test_async_integration.o

# target to build an object file
test_async_integration.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/async_memory_tests.dir/build.make CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.o
.PHONY : test_async_integration.cpp.o

test_async_integration.i: test_async_integration.cpp.i
.PHONY : test_async_integration.i

# target to preprocess a source file
test_async_integration.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/async_memory_tests.dir/build.make CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.i
.PHONY : test_async_integration.cpp.i

test_async_integration.s: test_async_integration.cpp.s
.PHONY : test_async_integration.s

# target to generate assembly for a file
test_async_integration.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/async_memory_tests.dir/build.make CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.s
.PHONY : test_async_integration.cpp.s

test_async_performance.o: test_async_performance.cpp.o
.PHONY : test_async_performance.o

# target to build an object file
test_async_performance.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/async_memory_tests.dir/build.make CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.o
.PHONY : test_async_performance.cpp.o

test_async_performance.i: test_async_performance.cpp.i
.PHONY : test_async_performance.i

# target to preprocess a source file
test_async_performance.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/async_memory_tests.dir/build.make CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.i
.PHONY : test_async_performance.cpp.i

test_async_performance.s: test_async_performance.cpp.s
.PHONY : test_async_performance.s

# target to generate assembly for a file
test_async_performance.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/async_memory_tests.dir/build.make CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.s
.PHONY : test_async_performance.cpp.s

test_async_stress.o: test_async_stress.cpp.o
.PHONY : test_async_stress.o

# target to build an object file
test_async_stress.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/async_memory_tests.dir/build.make CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.o
.PHONY : test_async_stress.cpp.o

test_async_stress.i: test_async_stress.cpp.i
.PHONY : test_async_stress.i

# target to preprocess a source file
test_async_stress.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/async_memory_tests.dir/build.make CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.i
.PHONY : test_async_stress.cpp.i

test_async_stress.s: test_async_stress.cpp.s
.PHONY : test_async_stress.s

# target to generate assembly for a file
test_async_stress.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/async_memory_tests.dir/build.make CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.s
.PHONY : test_async_stress.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... async_memory_tests"
	@echo "... test_async_comprehensive.o"
	@echo "... test_async_comprehensive.i"
	@echo "... test_async_comprehensive.s"
	@echo "... test_async_integration.o"
	@echo "... test_async_integration.i"
	@echo "... test_async_integration.s"
	@echo "... test_async_performance.o"
	@echo "... test_async_performance.i"
	@echo "... test_async_performance.s"
	@echo "... test_async_stress.o"
	@echo "... test_async_stress.i"
	@echo "... test_async_stress.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

