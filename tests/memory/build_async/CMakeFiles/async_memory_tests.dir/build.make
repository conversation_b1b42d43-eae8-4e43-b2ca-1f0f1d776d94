# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Atom/tests/memory

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Atom/tests/memory/build_async

# Include any dependencies generated for this target.
include CMakeFiles/async_memory_tests.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/async_memory_tests.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/async_memory_tests.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/async_memory_tests.dir/flags.make

CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.o: CMakeFiles/async_memory_tests.dir/flags.make
CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.o: /home/<USER>/Atom/tests/memory/test_async_comprehensive.cpp
CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.o: CMakeFiles/async_memory_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Atom/tests/memory/build_async/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.o -MF CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.o.d -o CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.o -c /home/<USER>/Atom/tests/memory/test_async_comprehensive.cpp

CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Atom/tests/memory/test_async_comprehensive.cpp > CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.i

CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Atom/tests/memory/test_async_comprehensive.cpp -o CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.s

CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.o: CMakeFiles/async_memory_tests.dir/flags.make
CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.o: /home/<USER>/Atom/tests/memory/test_async_integration.cpp
CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.o: CMakeFiles/async_memory_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Atom/tests/memory/build_async/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.o -MF CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.o.d -o CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.o -c /home/<USER>/Atom/tests/memory/test_async_integration.cpp

CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Atom/tests/memory/test_async_integration.cpp > CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.i

CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Atom/tests/memory/test_async_integration.cpp -o CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.s

CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.o: CMakeFiles/async_memory_tests.dir/flags.make
CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.o: /home/<USER>/Atom/tests/memory/test_async_performance.cpp
CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.o: CMakeFiles/async_memory_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Atom/tests/memory/build_async/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.o -MF CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.o.d -o CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.o -c /home/<USER>/Atom/tests/memory/test_async_performance.cpp

CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Atom/tests/memory/test_async_performance.cpp > CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.i

CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Atom/tests/memory/test_async_performance.cpp -o CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.s

CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.o: CMakeFiles/async_memory_tests.dir/flags.make
CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.o: /home/<USER>/Atom/tests/memory/test_async_stress.cpp
CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.o: CMakeFiles/async_memory_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Atom/tests/memory/build_async/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.o -MF CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.o.d -o CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.o -c /home/<USER>/Atom/tests/memory/test_async_stress.cpp

CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Atom/tests/memory/test_async_stress.cpp > CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.i

CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Atom/tests/memory/test_async_stress.cpp -o CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.s

# Object files for target async_memory_tests
async_memory_tests_OBJECTS = \
"CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.o" \
"CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.o" \
"CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.o" \
"CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.o"

# External object files for target async_memory_tests
async_memory_tests_EXTERNAL_OBJECTS =

async_memory_tests: CMakeFiles/async_memory_tests.dir/test_async_comprehensive.cpp.o
async_memory_tests: CMakeFiles/async_memory_tests.dir/test_async_integration.cpp.o
async_memory_tests: CMakeFiles/async_memory_tests.dir/test_async_performance.cpp.o
async_memory_tests: CMakeFiles/async_memory_tests.dir/test_async_stress.cpp.o
async_memory_tests: CMakeFiles/async_memory_tests.dir/build.make
async_memory_tests: /usr/lib/x86_64-linux-gnu/libgtest.a
async_memory_tests: /usr/lib/x86_64-linux-gnu/libgtest_main.a
async_memory_tests: /usr/lib/x86_64-linux-gnu/libgtest.a
async_memory_tests: CMakeFiles/async_memory_tests.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Atom/tests/memory/build_async/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX executable async_memory_tests"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/async_memory_tests.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/async_memory_tests.dir/build: async_memory_tests
.PHONY : CMakeFiles/async_memory_tests.dir/build

CMakeFiles/async_memory_tests.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/async_memory_tests.dir/cmake_clean.cmake
.PHONY : CMakeFiles/async_memory_tests.dir/clean

CMakeFiles/async_memory_tests.dir/depend:
	cd /home/<USER>/Atom/tests/memory/build_async && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Atom/tests/memory /home/<USER>/Atom/tests/memory /home/<USER>/Atom/tests/memory/build_async /home/<USER>/Atom/tests/memory/build_async /home/<USER>/Atom/tests/memory/build_async/CMakeFiles/async_memory_tests.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/async_memory_tests.dir/depend

